FRAS_env 项目文件结构
=====================================

FRAS_env/
├── 主要Python文件
│   ├── 128_eigenvector_collector.py
│   ├── 2FRAS.py
│   ├── 3FRAS.py
│   ├── 4FRAS.py
│   ├── FRAS.py
│   ├── FEFE.py
│   ├── Facial_Recognition_Attendance_System.py
│   ├── db_connector.py
│   ├── gpio_control.py
│   ├── system_manager.py
│   └── web_server.py
│
├── 配置文件
│   ├── requirements.txt
│   ├── index.php
│   ├── README.md
│   └── README_ZH.md
│
├── 日志文件
│   ├── FEFE.log
│   └── system_manager.log
│
├── 数据目录
│   ├── Encoding_DataSet/
│   │   ├── GAVIN CHANG.json
│   │   ├── Gan Boon Siang.json
│   │   ├── LOW ZHI XIANG.json
│   │   ├── Low Suzanne.json
│   │   ├── Ng Jia Le.json
│   │   ├── Ong <PERSON>.json
│   │   ├── PANG BIN BIN.json
│   │   ├── Tsuki.json
│   │   └── wongkinfei.json
│   │
│   ├── Image_DataSet/
│   │   ├── GAVIN CHANG/
│   │   │   ├── GAVIN CHANG_01_20250515_143845.jpg
│   │   │   ├── GAVIN CHANG_02_20250515_143845.jpg
│   │   │   └── ... (共25张图片)
│   │   ├── Gan Boon Siang/
│   │   │   └── ... (共25张图片)
│   │   ├── LOW ZHI XIANG/
│   │   │   └── ... (共25张图片)
│   │   ├── Low Suzanne/
│   │   │   └── ... (共25张图片)
│   │   ├── Ng Jia Le/
│   │   │   └── ... (共25张图片)
│   │   ├── Ong Hong Yao/
│   │   │   └── ... (共25张图片)
│   │   ├── PANG BIN BIN/
│   │   │   └── ... (共25张图片)
│   │   ├── Tsuki/
│   │   │   └── ... (共25张图片)
│   │   ├── wongkinfei/
│   │   │   └── ... (共25张图片)
│   │   └── LAM HUI XIN/
│   │
│   ├── Profile_Pictures/
│   │   ├── GAVIN CHANG.jpg
│   │   ├── Gan Boon Siang.jpg
│   │   ├── LAM HUI XIN.jpg
│   │   ├── LOW ZHI XIANG.jpg
│   │   ├── Low Suzanne.jpg
│   │   ├── Ng Jia Le.jpg
│   │   ├── Ong Hong Yao.jpg
│   │   ├── PANG BIN BIN.jpg
│   │   ├── Tsuki.jpg
│   │   └── wongkinfei.jpg
│   │
│   ├── logs/
│   │   ├── system_monitor_2025-05-17.log
│   │   ├── system_monitor_2025-05-20.log
│   │   ├── system_monitor_2025-05-21.log
│   │   ├── system_monitor_2025-05-22.log
│   │   ├── system_monitor_2025-05-23.log
│   │   ├── system_monitor_2025-05-24.log
│   │   └── system_monitor_2025-05-26.log
│   │
│   └── Static_Faces/
│
├── 模型文件
│   └── model/
│       ├── deploy.prototxt
│       └── res10_300x300_ssd_iter_140000.caffemodel
│
├── Web应用程序
│   └── HTML/
│       ├── 主要PHP文件
│       │   ├── index.php
│       │   ├── login.php
│       │   ├── register.php
│       │   ├── dashboard.php
│       │   ├── image_acquisition.php
│       │   ├── edit.php
│       │   ├── remove.php
│       │   ├── search.php
│       │   ├── log.php
│       │   ├── statistics.php
│       │   ├── settings.php
│       │   ├── monitoring.php
│       │   ├── system_management.php
│       │   ├── addition.php
│       │   ├── logout.php
│       │   └── 404.php
│       │
│       ├── AJAX处理文件
│       │   ├── login-ajax.php
│       │   ├── register-ajax.php
│       │   ├── image_acquisition-ajax.php
│       │   └── log-ajax.php
│       │
│       ├── 配置和数据库
│       │   ├── config.php
│       │   └── Facial_Recognition_Attendance_System.sql
│       │
│       ├── 静态资源
│       │   ├── icon.png
│       │   └── Raspberry-Pi.png
│       │
│       ├── CSS样式文件
│       │   └── css/
│       │       ├── login-style.css       # 登录页面样式
│       │       ├── register-style.css    # 注册页面样式
│       │       ├── dashboard-style.css   # 仪表板样式
│       │       ├── image-acquisition-style.css  # 图像采集样式
│       │       ├── edit-style.css        # 编辑页面样式
│       │       ├── remove-style.css      # 删除页面样式
│       │       ├── search-style.css      # 搜索页面样式
│       │       ├── log-style.css         # 日志页面样式
│       │       ├── statistics-style.css  # 统计页面样式
│       │       ├── settings-style.css    # 设置页面样式
│       │       ├── monitoring.css        # 监控页面样式
│       │       ├── system-management-style.css  # 系统管理样式
│       │       ├── addition-style.css    # 添加页面样式
│       │       ├── 404-style.css         # 404页面样式
│       │       ├── slidebar.css          # 侧边栏样式
│       │       ├── themes.css            # 主题样式
│       │       └── notifications.css     # 通知样式
│       │
│       ├── JavaScript文件
│       │   └── js/
│       │       ├── notifications.js      # 通知功能
│       │       ├── theme-loader.js       # 主题加载器
│       │       └── theme-switcher.js     # 主题切换器
│       │
│       ├── 语言文件
│       │   └── lang/
│       │       ├── en.php                # 英文语言包
│       │       └── zh.php                # 中文语言包
│       │
│       └── 包含文件
│           └── includes/
│               ├── sidebar.php           # 侧边栏组件
│               ├── theme-loader.php      # 主题加载器
│               ├── language-loader.php   # 语言加载器
│               ├── notifications-loader.php  # 通知加载器
│               ├── notification-functions.php  # 通知功能
│               ├── log-functions.php     # 日志功能
│               └── date-formatter.php    # 日期格式化器
│
└── 旧测试文件
    └── old test file/                    # 旧版本测试文件目录
        ├── 2camera.py                    # 双摄像头测试
        ├── FRAS copy.py                  # FRAS副本
        ├── FRAS.bk.py                    # FRAS备份
        ├── FRAS.py                       # 旧版FRAS
        ├── FaceDetection.py              # 人脸检测测试
        ├── default_avatar.jpg            # 默认头像
        ├── dual_camera_enhanced_test1.py # 增强双摄像头测试
        ├── dualcamera.py                 # 双摄像头
        ├── enhanced_test1.py             # 增强测试1
        ├── gpio_control.py               # GPIO控制测试
        ├── led.py                        # LED测试
        ├── led2.py                       # LED测试2
        ├── led3.py                       # LED测试3
        ├── newFD.py                      # 新人脸检测
        ├── test1.py                      # 测试1
        ├── test2.py                      # 测试2
        ├── test3.py                      # 测试3
        ├── test3bk.py                    # 测试3备份
        ├── test4.py                      # 测试4
        ├── test4bk.py                    # 测试4备份
        └── traffic_light.py              # 交通灯测试

注意：
- 虚拟环境相关文件（bin/, lib/, include/, share/, __pycache__/, pyvenv.cfg）已排除
- 每个用户在Image_DataSet目录下都有25张训练图片
- 系统支持多语言（中文/英文）
- 包含完整的Web管理界面
- 具有人脸识别、考勤管理、用户管理等功能
