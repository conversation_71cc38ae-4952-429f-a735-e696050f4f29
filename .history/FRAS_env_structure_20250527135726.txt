FRAS_env Project File Structure
=====================================

FRAS_env/
├── 128_eigenvector_collector.py
├── 2FRAS.py
├── 3FRAS.py
├── 4FRAS.py
├── FRAS.py
├── FEFE.py
├── Facial_Recognition_Attendance_System.py
├── db_connector.py
├── gpio_control.py
├── system_manager.py
├── web_server.py
├── requirements.txt
├── index.php
├── README.md
├── README_ZH.md
│   ├── Encoding_DataSet/
│   │   ├── GAVIN CHANG.json
│   │   ├── Gan <PERSON>.json
│   │   ├── LOW ZHI XIANG.json
│   │   ├── Low Suzanne.json
│   │   ├── Ng Jia Le.json
│   │   ├── Ong Hong <PERSON>.json
│   │   ├── PANG BIN BIN.json
│   │   ├── Tsuki.json
│   │   └── wongkinfei.json
│   │
│   ├── Image_DataSet/
│   │   ├── GAVIN CHANG/
│   │   │   ├── GAVIN CHANG_01_20250515_143845.jpg
│   │   │   ├── GAVIN CHANG_02_20250515_143845.jpg
│   │   │   └── ... (25 images total)
│   │   ├── <PERSON><PERSON>/
│   │   │   └── ... (25 images total)
│   │   ├── LOW ZHI XIANG/
│   │   │   └── ... (25 images total)
│   │   ├── Low Suzanne/
│   │   │   └── ... (25 images total)
│   │   ├── Ng Jia Le/
│   │   │   └── ... (25 images total)
│   │   ├── Ong Hong Yao/
│   │   │   └── ... (25 images total)
│   │   ├── PANG BIN BIN/
│   │   │   └── ... (25 images total)
│   │   ├── Tsuki/
│   │   │   └── ... (25 images total)
│   │   ├── wongkinfei/
│   │   │   └── ... (25 images total)
│   │   └── LAM HUI XIN/
│   │
│   ├── Profile_Pictures/
│   │   ├── GAVIN CHANG.jpg
│   │   ├── Gan Boon Siang.jpg
│   │   ├── LAM HUI XIN.jpg
│   │   ├── LOW ZHI XIANG.jpg
│   │   ├── Low Suzanne.jpg
│   │   ├── Ng Jia Le.jpg
│   │   ├── Ong Hong Yao.jpg
│   │   ├── PANG BIN BIN.jpg
│   │   ├── Tsuki.jpg
│   │   └── wongkinfei.jpg
│   │
│   └── Static_Faces/
│
├── Model Files
│   └── model/
│       ├── deploy.prototxt
│       └── res10_300x300_ssd_iter_140000.caffemodel
│
├── Web Application
│   └── HTML/
│       ├── Main PHP Files
│       │   ├── index.php
│       │   ├── login.php
│       │   ├── register.php
│       │   ├── dashboard.php
│       │   ├── image_acquisition.php
│       │   ├── edit.php
│       │   ├── remove.php
│       │   ├── search.php
│       │   ├── log.php
│       │   ├── statistics.php
│       │   ├── settings.php
│       │   ├── monitoring.php
│       │   ├── system_management.php
│       │   ├── addition.php
│       │   ├── logout.php
│       │   └── 404.php
│       │
│       ├── AJAX Handler Files
│       │   ├── login-ajax.php
│       │   ├── register-ajax.php
│       │   ├── image_acquisition-ajax.php
│       │   └── log-ajax.php
│       │
│       ├── Configuration and Database
│       │   ├── config.php
│       │   └── Facial_Recognition_Attendance_System.sql
│       │
│       ├── Static Resources
│       │   ├── icon.png
│       │   └── Raspberry-Pi.png
│       │
│       ├── CSS Style Files
│       │   └── css/
│       │       ├── login-style.css
│       │       ├── register-style.css
│       │       ├── dashboard-style.css
│       │       ├── image-acquisition-style.css
│       │       ├── edit-style.css
│       │       ├── remove-style.css
│       │       ├── search-style.css
│       │       ├── log-style.css
│       │       ├── statistics-style.css
│       │       ├── settings-style.css
│       │       ├── monitoring.css
│       │       ├── system-management-style.css
│       │       ├── addition-style.css
│       │       ├── 404-style.css
│       │       ├── slidebar.css
│       │       ├── themes.css
│       │       └── notifications.css
│       │
│       ├── JavaScript Files
│       │   └── js/
│       │       ├── notifications.js
│       │       ├── theme-loader.js
│       │       └── theme-switcher.js
│       │
│       ├── Language Files
│       │   └── lang/
│       │       ├── en.php
│       │       └── zh.php
│       │
│       └── Include Files
│           └── includes/
│               ├── sidebar.php
│               ├── theme-loader.php
│               ├── language-loader.php
│               ├── notifications-loader.php
│               ├── notification-functions.php
│               ├── log-functions.php
│               └── date-formatter.php