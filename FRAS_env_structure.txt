📁 FRAS_env/
│
├── 🐍 Core Python Files
│   ├── 🎯 FRAS.py                    # Main face recognition system
│   ├── 🔧 FEFE.py                    # FEFE module
│   ├── 🗄️  db_connector.py           # Database connector
│   ├── ⚡ gpio_control.py            # GPIO control module
│   └── 🌐 web_server.py              # Web server
│
├── 📊 Data Storage
│   ├── 🧠 Encoding_DataSet/          # Face encoding vectors
│   │   └── 📄 Person1.json
│   │
│   ├── 📸 Image_DataSet/             # Training images
│   │   └── 👤 Person1/
│   │       └── 🖼️  Person1.jpg
│   │
│   └── 🖼️  Profile_Pictures/         # User profile photos
│       └── 👤 Person1.jpg
│
└── 🌐 Web Application (HTML/)
    │
    ├── 📄 Core Pages
    │   ├── 🏠 index.php              # Homepage
    │   ├── 🔐 login.php              # Login page
    │   ├── 📝 register.php           # Registration page
    │   ├── 📊 dashboard.php          # Main dashboard
    │   ├── 📷 image_acquisition.php  # Image capture
    │   ├── ✏️  edit.php               # Edit user data
    │   ├── 🗑️  remove.php             # Delete users
    │   ├── 🔍 search.php             # Search functionality
    │   ├── 📋 log.php                # System logs
    │   ├── 📈 statistics.php         # Statistics view
    │   ├── ⚙️  settings.php           # System settings
    │   ├── 📡 monitoring.php         # System monitoring
    │   ├── 🛠️  system_management.php  # System management
    │   ├── ➕ addition.php            # Add new users
    │   ├── 🚪 logout.php             # Logout handler
    │   └── ❌ 404.php                # Error page
    │
    ├── ⚡ AJAX Handlers
    │   ├── 🔐 login-ajax.php
    │   ├── 📝 register-ajax.php
    │   ├── 📷 image_acquisition-ajax.php
    │   └── 📋 log-ajax.php
    │
    ├── ⚙️  Configuration & Database
    │   ├── 🔧 config.php             # Main configuration
    │   └── 🗄️  Facial_Recognition_Attendance_System.sql
    │
    ├── 🎨 Static Resources
    │   ├── 🎯 icon.png               # Site icon
    │   └── 🍓 Raspberry-Pi.png       # Pi logo
    │
    ├── 🎨 Stylesheets (css/)
    │   ├── 🔐 login-style.css
    │   ├── 📝 register-style.css
    │   ├── 📊 dashboard-style.css
    │   ├── 📷 image-acquisition-style.css
    │   ├── ✏️  edit-style.css
    │   ├── 🗑️  remove-style.css
    │   ├── 🔍 search-style.css
    │   ├── 📋 log-style.css
    │   ├── 📈 statistics-style.css
    │   ├── ⚙️  settings-style.css
    │   ├── 📡 monitoring.css
    │   ├── 🛠️  system-management-style.css
    │   ├── ➕ addition-style.css
    │   ├── ❌ 404-style.css
    │   ├── 📱 slidebar.css
    │   ├── 🎨 themes.css
    │   └── 🔔 notifications.css
    │
    ├── ⚡ JavaScript (js/)
    │   ├── 🔔 notifications.js       # Notification system
    │   ├── 🎨 theme-loader.js        # Theme management
    │   └── 🔄 theme-switcher.js      # Theme switching
    │
    ├── 🌍 Languages (lang/)
    │   ├── 🇺🇸 en.php                # English translations
    │   └── 🇨🇳 zh.php                # Chinese translations
    │
    └── 📦 Includes (includes/)
        ├── 📱 sidebar.php            # Sidebar component
        ├── 🎨 theme-loader.php       # Theme loader
        ├── 🌍 language-loader.php    # Language loader
        ├── 🔔 notifications-loader.php
        ├── 🔔 notification-functions.php
        ├── 📋 log-functions.php      # Log utilities
        └── 📅 date-formatter.php     # Date formatting