FRAS_env 项目文件结构
=====================================

FRAS_env/
├── 主要Python文件
│   ├── 128_eigenvector_collector.py      # 128维特征向量收集器
│   ├── 2FRAS.py                          # 人脸识别系统版本2
│   ├── 3FRAS.py                          # 人脸识别系统版本3
│   ├── 4FRAS.py                          # 人脸识别系统版本4
│   ├── FRAS.py                           # 主要人脸识别系统
│   ├── FEFE.py                           # FEFE模块
│   ├── Facial_Recognition_Attendance_System.py  # 人脸识别考勤系统
│   ├── db_connector.py                   # 数据库连接器
│   ├── gpio_control.py                   # GPIO控制模块
│   ├── system_manager.py                 # 系统管理器
│   └── web_server.py                     # Web服务器
│
├── 配置文件
│   ├── requirements.txt                  # Python依赖包列表
│   ├── index.php                         # 主页入口文件
│   ├── README.md                         # 英文说明文档
│   └── README_ZH.md                      # 中文说明文档
│
├── 日志文件
│   ├── FEFE.log                          # FEFE模块日志
│   └── system_manager.log                # 系统管理器日志
│
├── 数据目录
│   ├── Encoding_DataSet/                 # 编码数据集目录
│   │   ├── GAVIN CHANG.json
│   │   ├── Gan Boon Siang.json
│   │   ├── LOW ZHI XIANG.json
│   │   ├── Low Suzanne.json
│   │   ├── Ng Jia Le.json
│   │   ├── Ong Hong Yao.json
│   │   ├── PANG BIN BIN.json
│   │   ├── Tsuki.json
│   │   └── wongkinfei.json
│   │
│   ├── Image_DataSet/                    # 图像数据集目录
│   │   ├── GAVIN CHANG/                  # 个人图像文件夹
│   │   │   ├── GAVIN CHANG_01_20250515_143845.jpg
│   │   │   ├── GAVIN CHANG_02_20250515_143845.jpg
│   │   │   └── ... (共25张图片)
│   │   ├── Gan Boon Siang/
│   │   │   └── ... (共25张图片)
│   │   ├── LOW ZHI XIANG/
│   │   │   └── ... (共25张图片)
│   │   ├── Low Suzanne/
│   │   │   └── ... (共25张图片)
│   │   ├── Ng Jia Le/
│   │   │   └── ... (共25张图片)
│   │   ├── Ong Hong Yao/
│   │   │   └── ... (共25张图片)
│   │   ├── PANG BIN BIN/
│   │   │   └── ... (共25张图片)
│   │   ├── Tsuki/
│   │   │   └── ... (共25张图片)
│   │   ├── wongkinfei/
│   │   │   └── ... (共25张图片)
│   │   └── LAM HUI XIN/                  # 空文件夹
│   │
│   ├── Profile_Pictures/                 # 头像图片目录
│   │   ├── GAVIN CHANG.jpg
│   │   ├── Gan Boon Siang.jpg
│   │   ├── LAM HUI XIN.jpg
│   │   ├── LOW ZHI XIANG.jpg
│   │   ├── Low Suzanne.jpg
│   │   ├── Ng Jia Le.jpg
│   │   ├── Ong Hong Yao.jpg
│   │   ├── PANG BIN BIN.jpg
│   │   ├── Tsuki.jpg
│   │   └── wongkinfei.jpg
│   │
│   ├── logs/                             # 系统日志目录
│   │   ├── system_monitor_2025-05-17.log
│   │   ├── system_monitor_2025-05-20.log
│   │   ├── system_monitor_2025-05-21.log
│   │   ├── system_monitor_2025-05-22.log
│   │   ├── system_monitor_2025-05-23.log
│   │   ├── system_monitor_2025-05-24.log
│   │   └── system_monitor_2025-05-26.log
│   │
│   └── Static_Faces/                     # 静态人脸目录
│
├── 模型文件
│   └── model/                            # 机器学习模型目录
│       ├── deploy.prototxt               # 部署配置文件
│       └── res10_300x300_ssd_iter_140000.caffemodel  # 人脸检测模型
│
├── Web应用程序
│   └── HTML/                             # Web界面目录
│       ├── 主要PHP文件
│       │   ├── index.php                 # 主页
│       │   ├── login.php                 # 登录页面
│       │   ├── register.php              # 注册页面
│       │   ├── dashboard.php             # 仪表板
│       │   ├── image_acquisition.php     # 图像采集页面
│       │   ├── edit.php                  # 编辑页面
│       │   ├── remove.php                # 删除页面
│       │   ├── search.php                # 搜索页面
│       │   ├── log.php                   # 日志页面
│       │   ├── statistics.php            # 统计页面
│       │   ├── settings.php              # 设置页面
│       │   ├── monitoring.php            # 监控页面
│       │   ├── system_management.php     # 系统管理页面
│       │   ├── addition.php              # 添加页面
│       │   ├── logout.php                # 登出页面
│       │   └── 404.php                   # 404错误页面
│       │
│       ├── AJAX处理文件
│       │   ├── login-ajax.php            # 登录AJAX处理
│       │   ├── register-ajax.php         # 注册AJAX处理
│       │   ├── image_acquisition-ajax.php # 图像采集AJAX处理
│       │   └── log-ajax.php              # 日志AJAX处理
│       │
│       ├── 配置和数据库
│       │   ├── config.php                # 配置文件
│       │   └── Facial_Recognition_Attendance_System.sql  # 数据库结构
│       │
│       ├── 静态资源
│       │   ├── icon.png                  # 网站图标
│       │   └── Raspberry-Pi.png          # 树莓派图片
│       │
│       ├── CSS样式文件
│       │   └── css/
│       │       ├── login-style.css       # 登录页面样式
│       │       ├── register-style.css    # 注册页面样式
│       │       ├── dashboard-style.css   # 仪表板样式
│       │       ├── image-acquisition-style.css  # 图像采集样式
│       │       ├── edit-style.css        # 编辑页面样式
│       │       ├── remove-style.css      # 删除页面样式
│       │       ├── search-style.css      # 搜索页面样式
│       │       ├── log-style.css         # 日志页面样式
│       │       ├── statistics-style.css  # 统计页面样式
│       │       ├── settings-style.css    # 设置页面样式
│       │       ├── monitoring.css        # 监控页面样式
│       │       ├── system-management-style.css  # 系统管理样式
│       │       ├── addition-style.css    # 添加页面样式
│       │       ├── 404-style.css         # 404页面样式
│       │       ├── slidebar.css          # 侧边栏样式
│       │       ├── themes.css            # 主题样式
│       │       └── notifications.css     # 通知样式
│       │
│       ├── JavaScript文件
│       │   └── js/
│       │       ├── notifications.js      # 通知功能
│       │       ├── theme-loader.js       # 主题加载器
│       │       └── theme-switcher.js     # 主题切换器
│       │
│       ├── 语言文件
│       │   └── lang/
│       │       ├── en.php                # 英文语言包
│       │       └── zh.php                # 中文语言包
│       │
│       └── 包含文件
│           └── includes/
│               ├── sidebar.php           # 侧边栏组件
│               ├── theme-loader.php      # 主题加载器
│               ├── language-loader.php   # 语言加载器
│               ├── notifications-loader.php  # 通知加载器
│               ├── notification-functions.php  # 通知功能
│               ├── log-functions.php     # 日志功能
│               └── date-formatter.php    # 日期格式化器
│
└── 旧测试文件
    └── old test file/                    # 旧版本测试文件目录
        ├── 2camera.py                    # 双摄像头测试
        ├── FRAS copy.py                  # FRAS副本
        ├── FRAS.bk.py                    # FRAS备份
        ├── FRAS.py                       # 旧版FRAS
        ├── FaceDetection.py              # 人脸检测测试
        ├── default_avatar.jpg            # 默认头像
        ├── dual_camera_enhanced_test1.py # 增强双摄像头测试
        ├── dualcamera.py                 # 双摄像头
        ├── enhanced_test1.py             # 增强测试1
        ├── gpio_control.py               # GPIO控制测试
        ├── led.py                        # LED测试
        ├── led2.py                       # LED测试2
        ├── led3.py                       # LED测试3
        ├── newFD.py                      # 新人脸检测
        ├── test1.py                      # 测试1
        ├── test2.py                      # 测试2
        ├── test3.py                      # 测试3
        ├── test3bk.py                    # 测试3备份
        ├── test4.py                      # 测试4
        ├── test4bk.py                    # 测试4备份
        └── traffic_light.py              # 交通灯测试

注意：
- 虚拟环境相关文件（bin/, lib/, include/, share/, __pycache__/, pyvenv.cfg）已排除
- 每个用户在Image_DataSet目录下都有25张训练图片
- 系统支持多语言（中文/英文）
- 包含完整的Web管理界面
- 具有人脸识别、考勤管理、用户管理等功能
