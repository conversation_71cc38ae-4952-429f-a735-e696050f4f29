FRAS_env 项目文件结构
=====================================

FRAS_env/
├── 主要Python文件
│   ├── 128_eigenvector_collector.py
│   ├── 2FRAS.py
│   ├── 3FRAS.py
│   ├── 4FRAS.py
│   ├── FRAS.py
│   ├── FEFE.py
│   ├── Facial_Recognition_Attendance_System.py
│   ├── db_connector.py
│   ├── gpio_control.py
│   ├── system_manager.py
│   └── web_server.py
│
├── 配置文件
│   ├── requirements.txt
│   ├── index.php
│   ├── README.md
│   └── README_ZH.md
│
├── 日志文件
│   ├── FEFE.log
│   └── system_manager.log
│
├── 数据目录
│   ├── Encoding_DataSet/
│   │   ├── GAVIN CHANG.json
│   │   ├── Gan Boon Siang.json
│   │   ├── LOW ZHI XIANG.json
│   │   ├── Low Suzanne.json
│   │   ├── Ng Jia Le.json
│   │   ├── Ong <PERSON>.json
│   │   ├── PANG BIN BIN.json
│   │   ├── Tsuki.json
│   │   └── wongkinfei.json
│   │
│   ├── Image_DataSet/
│   │   ├── GAVIN CHANG/
│   │   │   ├── GAVIN CHANG_01_20250515_143845.jpg
│   │   │   ├── GAVIN CHANG_02_20250515_143845.jpg
│   │   │   └── ... (共25张图片)
│   │   ├── Gan Boon Siang/
│   │   │   └── ... (共25张图片)
│   │   ├── LOW ZHI XIANG/
│   │   │   └── ... (共25张图片)
│   │   ├── Low Suzanne/
│   │   │   └── ... (共25张图片)
│   │   ├── Ng Jia Le/
│   │   │   └── ... (共25张图片)
│   │   ├── Ong Hong Yao/
│   │   │   └── ... (共25张图片)
│   │   ├── PANG BIN BIN/
│   │   │   └── ... (共25张图片)
│   │   ├── Tsuki/
│   │   │   └── ... (共25张图片)
│   │   ├── wongkinfei/
│   │   │   └── ... (共25张图片)
│   │   └── LAM HUI XIN/
│   │
│   ├── Profile_Pictures/
│   │   ├── GAVIN CHANG.jpg
│   │   ├── Gan Boon Siang.jpg
│   │   ├── LAM HUI XIN.jpg
│   │   ├── LOW ZHI XIANG.jpg
│   │   ├── Low Suzanne.jpg
│   │   ├── Ng Jia Le.jpg
│   │   ├── Ong Hong Yao.jpg
│   │   ├── PANG BIN BIN.jpg
│   │   ├── Tsuki.jpg
│   │   └── wongkinfei.jpg
│   │
│   ├── logs/
│   │   ├── system_monitor_2025-05-17.log
│   │   ├── system_monitor_2025-05-20.log
│   │   ├── system_monitor_2025-05-21.log
│   │   ├── system_monitor_2025-05-22.log
│   │   ├── system_monitor_2025-05-23.log
│   │   ├── system_monitor_2025-05-24.log
│   │   └── system_monitor_2025-05-26.log
│   │
│   └── Static_Faces/
│
├── 模型文件
│   └── model/
│       ├── deploy.prototxt
│       └── res10_300x300_ssd_iter_140000.caffemodel
│
├── Web应用程序
│   └── HTML/
│       ├── 主要PHP文件
│       │   ├── index.php
│       │   ├── login.php
│       │   ├── register.php
│       │   ├── dashboard.php
│       │   ├── image_acquisition.php
│       │   ├── edit.php
│       │   ├── remove.php
│       │   ├── search.php
│       │   ├── log.php
│       │   ├── statistics.php
│       │   ├── settings.php
│       │   ├── monitoring.php
│       │   ├── system_management.php
│       │   ├── addition.php
│       │   ├── logout.php
│       │   └── 404.php
│       │
│       ├── AJAX处理文件
│       │   ├── login-ajax.php
│       │   ├── register-ajax.php
│       │   ├── image_acquisition-ajax.php
│       │   └── log-ajax.php
│       │
│       ├── 配置和数据库
│       │   ├── config.php
│       │   └── Facial_Recognition_Attendance_System.sql
│       │
│       ├── 静态资源
│       │   ├── icon.png
│       │   └── Raspberry-Pi.png
│       │
│       ├── CSS样式文件
│       │   └── css/
│       │       ├── login-style.css
│       │       ├── register-style.css
│       │       ├── dashboard-style.css
│       │       ├── image-acquisition-style.css
│       │       ├── edit-style.css
│       │       ├── remove-style.css
│       │       ├── search-style.css
│       │       ├── log-style.css
│       │       ├── statistics-style.css
│       │       ├── settings-style.css
│       │       ├── monitoring.css
│       │       ├── system-management-style.css
│       │       ├── addition-style.css
│       │       ├── 404-style.css
│       │       ├── slidebar.css
│       │       ├── themes.css
│       │       └── notifications.css
│       │
│       ├── JavaScript文件
│       │   └── js/
│       │       ├── notifications.js
│       │       ├── theme-loader.js
│       │       └── theme-switcher.js
│       │
│       ├── 语言文件
│       │   └── lang/
│       │       ├── en.php
│       │       └── zh.php
│       │
│       └── 包含文件
│           └── includes/
│               ├── sidebar.php
│               ├── theme-loader.php
│               ├── language-loader.php
│               ├── notifications-loader.php
│               ├── notification-functions.php
│               ├── log-functions.php
│               └── date-formatter.php
│
└── 旧测试文件
    └── old test file/                    # 旧版本测试文件目录
        ├── 2camera.py                    # 双摄像头测试
        ├── FRAS copy.py                  # FRAS副本
        ├── FRAS.bk.py                    # FRAS备份
        ├── FRAS.py                       # 旧版FRAS
        ├── FaceDetection.py              # 人脸检测测试
        ├── default_avatar.jpg            # 默认头像
        ├── dual_camera_enhanced_test1.py # 增强双摄像头测试
        ├── dualcamera.py                 # 双摄像头
        ├── enhanced_test1.py             # 增强测试1
        ├── gpio_control.py               # GPIO控制测试
        ├── led.py                        # LED测试
        ├── led2.py                       # LED测试2
        ├── led3.py                       # LED测试3
        ├── newFD.py                      # 新人脸检测
        ├── test1.py                      # 测试1
        ├── test2.py                      # 测试2
        ├── test3.py                      # 测试3
        ├── test3bk.py                    # 测试3备份
        ├── test4.py                      # 测试4
        ├── test4bk.py                    # 测试4备份
        └── traffic_light.py              # 交通灯测试

注意：
- 虚拟环境相关文件（bin/, lib/, include/, share/, __pycache__/, pyvenv.cfg）已排除
- 每个用户在Image_DataSet目录下都有25张训练图片
- 系统支持多语言（中文/英文）
- 包含完整的Web管理界面
- 具有人脸识别、考勤管理、用户管理等功能
