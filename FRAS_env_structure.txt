FRAS_env/
├── FRAS.py
├── FEFE.py
├── db_connector.py
├── gpio_control.py
├── web_server.py
│
├── Encoding_DataSet/
│   ├── Person1.json
├── Image_DataSet/
│   ├── Person1/
│   │   └──  Person1.jpg
├── Profile_Pictures/
│   └── Person1.jpg
└── HTML/
    ├── index.php
    ├── login.php
    ├── register.php
    ├── dashboard.php
    ├── image_acquisition.php
    ├── edit.php
    ├── remove.php
    ├── search.php
    ├── log.php
    ├── statistics.php
    ├── settings.php
    ├── monitoring.php
    ├── system_management.php
    ├── addition.php
    ├── logout.php
    ├── 404.php
    ├── login-ajax.php
    ├── register-ajax.php
    ├── image_acquisition-ajax.php
    ├── log-ajax.php
    ├── config.php
    ├── Facial_Recognition_Attendance_System.sql
    ├── icon.png
    ├── Raspberry-Pi.png
    ├── css/
    │   ├── login-style.css
    │   ├── register-style.css
    │   ├── dashboard-style.css
    │   ├── image-acquisition-style.css
    │   ├── edit-style.css
    │   ├── remove-style.css
    │   ├── search-style.css
    │   ├── log-style.css
    │   ├── statistics-style.css
    │   ├── settings-style.css
    │   ├── monitoring.css
    │   ├── system-management-style.css
    │   ├── addition-style.css
    │   ├── 404-style.css
    │   ├── slidebar.css
    │   ├── themes.css
    │   └── notifications.css
    ├── js/
    │   ├── notifications.js
    │   ├── theme-loader.js
    │   └── theme-switcher.js
    ├── lang/
    │   ├── en.php
    │   └── zh.php
    └── includes/
        ├── sidebar.php
        ├── theme-loader.php
        ├── language-loader.php
        ├── notifications-loader.php
        ├── notification-functions.php
        ├── log-functions.php
        └── date-formatter.php